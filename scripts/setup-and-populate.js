#!/usr/bin/env node

/**
 * Combined Directus Setup and Population Script
 * 
 * This script runs both the collection setup and data population in sequence.
 * It's a convenience script for first-time setup.
 * 
 * Usage:
 * 1. Make sure Directus is running (docker-compose up -d)
 * 2. Set DIRECTUS_URL and DIRECTUS_TOKEN in .env file
 * 3. Run: node scripts/setup-and-populate.js
 */

const { spawn } = require('child_process');
const path = require('path');

function runScript(scriptPath, description) {
  return new Promise((resolve, reject) => {
    console.log(`\n🚀 ${description}...`);
    console.log(`📄 Running: ${scriptPath}`);
    
    const child = spawn('node', [scriptPath], {
      stdio: 'inherit',
      cwd: process.cwd()
    });

    child.on('close', (code) => {
      if (code === 0) {
        console.log(`✅ ${description} completed successfully`);
        resolve();
      } else {
        console.error(`❌ ${description} failed with exit code ${code}`);
        reject(new Error(`<PERSON><PERSON><PERSON> failed with exit code ${code}`));
      }
    });

    child.on('error', (error) => {
      console.error(`❌ Error running ${description}:`, error.message);
      reject(error);
    });
  });
}

async function setupAndPopulate() {
  console.log('🎯 Directus Complete Setup & Data Population');
  console.log('============================================');
  
  try {
    // Step 1: Setup collections
    await runScript(
      path.join(__dirname, 'setup-directus.js'),
      'Setting up Directus collections and fields'
    );

    // Step 2: Populate data
    await runScript(
      path.join(__dirname, 'populate-directus-data.js'),
      'Populating collections with sample data'
    );

    console.log('\n🎉 Complete setup finished successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Collections created with all fields');
    console.log('✅ Sample data populated in all collections');
    console.log('\n🔗 Next steps:');
    console.log('1. Visit Directus admin: http://localhost:8055/admin');
    console.log('2. Review and customize the populated data');
    console.log('3. Upload images for skills, projects, and profile');
    console.log('4. Update your frontend to fetch from Directus API');

  } catch (error) {
    console.error('\n❌ Setup process failed:', error.message);
    console.log('\n🔧 Troubleshooting:');
    console.log('1. Make sure Directus is running: npm run docker:up');
    console.log('2. Check your .env file has DIRECTUS_TOKEN or admin credentials');
    console.log('3. Verify Directus is accessible at http://localhost:8055');
    console.log('4. Run individual scripts to isolate the issue:');
    console.log('   - npm run setup:directus');
    console.log('   - npm run populate:directus');
    process.exit(1);
  }
}

// Run the combined setup
setupAndPopulate();
