# Directus Setup Scripts

## Overview

This directory contains scripts for automating the setup of Directus collections and fields for the Portfolio 2025 project.

## Available Scripts

### `setup-directus.js`

This script creates all necessary collections and fields in Directus based on the project requirements.

## Prerequisites

1. Directus must be running (use `npm run docker:up` to start the Docker containers)
2. You must have a valid Directus admin token set in your `.env` file

## Usage

### Setting up Directus Collections

1. Make sure you have a `.env` file with the following variables:
   ```
   DIRECTUS_URL=http://localhost:8055
   DIRECTUS_TOKEN=your_directus_token_here
   ```

2. Get a static token from Directus:
   - Log in to Directus admin panel (http://localhost:8055/admin)
   - Go to User Settings > Token
   - Create a new token with admin privileges
   - Copy the token to your `.env` file

3. Run the setup script:
   ```bash
   npm run setup:directus
   ```

## Collections Created

The script creates the following collections:

1. **Profile** - Personal information (singleton)
   - Name, title, bio, avatar, resume file

2. **Skills** - Technical skills
   - Name, image, category (frontend, backend, database, devops, tools, cms)

3. **Expertise** - Areas of expertise
   - Text, background color, custom styling, slide/column positioning

4. **Experience** - Professional experience
   - Period, title, company, role, technologies (backend, frontend, platform, database), details, achievements

5. **Projects** - Portfolio projects
   - Name, category, image, URL, styling options (gradient, glass effect, overlay color, etc.)

6. **Education** - Education and certifications
   - Year, title, certificate file, certificate link

7. **Social Links** - Social media profiles
   - Platform, URL, icon

## Troubleshooting

- If you encounter errors about collections or fields already existing, you can safely ignore these
- For other errors, check that your Directus instance is running and your token has admin privileges
- Make sure the `dotenv` package is installed (`npm install dotenv --save-dev`)