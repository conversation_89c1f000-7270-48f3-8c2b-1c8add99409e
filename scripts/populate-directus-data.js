#!/usr/bin/env node

/**
 * Directus Data Population Script
 * 
 * This script populates the Directus collections with sample data for the Portfolio 2025 project.
 * It uses the Directus API to programmatically insert data into the collections.
 * 
 * Usage:
 * 1. Make sure Directus is running and collections are created (run setup-directus.js first)
 * 2. Set DIRECTUS_URL and DIRECTUS_TOKEN in .env file
 * 3. Run: node scripts/populate-directus-data.js
 */

require('dotenv').config();
const { createDirectus, rest, createItem, createItems, updateItem, authentication } = require('@directus/sdk');

// Configuration
const directusUrl = process.env.DIRECTUS_URL || 'http://localhost:8055';
const directusToken = process.env.DIRECTUS_TOKEN;
const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
const adminPassword = process.env.ADMIN_PASSWORD || 'admin';

if (!directusToken && (!adminEmail || !adminPassword)) {
  console.error('❌ Please set DIRECTUS_TOKEN or ADMIN_EMAIL/ADMIN_PASSWORD in your .env file');
  process.exit(1);
}

// Initialize Directus client
const directus = createDirectus(directusUrl).with(rest());

// Sample data for collections
const sampleData = {
  profile: {
    name: 'Huy Nguyen',
    title: 'Fullstack Developer',
    bio: 'Passionate fullstack developer with expertise in modern web technologies. I love creating scalable applications and solving complex problems with clean, efficient code.',
    email: '<EMAIL>',
    phone: '+65 9123 4567',
    location: 'Singapore',
    website: 'https://huynguyen.dev'
  },
  
  skills: [
    // Frontend Technologies
    { name: 'HTML5', category: 'frontend', sort: 1 },
    { name: 'CSS3', category: 'frontend', sort: 2 },
    { name: 'JavaScript', category: 'frontend', sort: 3 },
    { name: 'TypeScript', category: 'frontend', sort: 4 },
    { name: 'React.js', category: 'frontend', sort: 5 },
    { name: 'Vue.js', category: 'frontend', sort: 6 },
    { name: 'Next.js', category: 'frontend', sort: 7 },
    { name: 'Nuxt.js', category: 'frontend', sort: 8 },
    { name: 'Remix.js', category: 'frontend', sort: 9 },
    { name: 'Gatsby.js', category: 'frontend', sort: 10 },
    { name: 'Svelte', category: 'frontend', sort: 11 },
    { name: 'SCSS/Sass', category: 'frontend', sort: 12 },
    { name: 'TailwindCSS', category: 'frontend', sort: 13 },
    { name: 'Bootstrap', category: 'frontend', sort: 14 },
    
    // Backend Technologies
    { name: 'Node.js', category: 'backend', sort: 15 },
    { name: 'Express.js', category: 'backend', sort: 16 },
    { name: 'NestJS', category: 'backend', sort: 17 },
    { name: 'PHP', category: 'backend', sort: 18 },
    { name: 'Laravel', category: 'backend', sort: 19 },
    { name: 'Python', category: 'backend', sort: 20 },
    { name: 'Django', category: 'backend', sort: 21 },
    { name: 'FastAPI', category: 'backend', sort: 22 },
    
    // Databases
    { name: 'PostgreSQL', category: 'database', sort: 23 },
    { name: 'MySQL', category: 'database', sort: 24 },
    { name: 'MongoDB', category: 'database', sort: 25 },
    { name: 'Redis', category: 'database', sort: 26 },
    { name: 'SQLite', category: 'database', sort: 27 },
    
    // Cloud & DevOps
    { name: 'AWS', category: 'devops', sort: 28 },
    { name: 'Google Cloud', category: 'devops', sort: 29 },
    { name: 'Docker', category: 'devops', sort: 30 },
    { name: 'Kubernetes', category: 'devops', sort: 31 },
    { name: 'GitHub Actions', category: 'devops', sort: 32 },
    { name: 'GitLab CI', category: 'devops', sort: 33 },
    { name: 'Vercel', category: 'devops', sort: 34 },
    { name: 'Netlify', category: 'devops', sort: 35 },
    
    // Development Tools
    { name: 'Git', category: 'tools', sort: 36 },
    { name: 'VS Code', category: 'tools', sort: 37 },
    { name: 'Webpack', category: 'tools', sort: 38 },
    { name: 'Vite', category: 'tools', sort: 39 },
    { name: 'ESLint', category: 'tools', sort: 40 },
    { name: 'Prettier', category: 'tools', sort: 41 },
    { name: 'Jest', category: 'tools', sort: 42 },
    { name: 'Cypress', category: 'tools', sort: 43 },
    
    // CMS Platforms
    { name: 'WordPress', category: 'cms', sort: 44 },
    { name: 'Shopify', category: 'cms', sort: 45 },
    { name: 'Strapi', category: 'cms', sort: 46 },
    { name: 'Contentful', category: 'cms', sort: 47 },
    { name: 'Ghost', category: 'cms', sort: 48 },
    { name: 'Directus', category: 'cms', sort: 49 }
  ],
  
  expertise: [
    // Slide 1, Column 1
    { text: 'Experience with AWS services like EC2, LB, S3, R53, Cloudfront, ECS, VPC, Lambda', background_color: 'neutral_2_50', slide: 1, column: 1, sort: 1 },
    { text: 'Experience with Tencent / Huawei / Alibaba services like CVM/ECS, LB, CDN, OBS, DNS', background_color: 'primary_3_15', slide: 1, column: 1, sort: 2 },
    { text: 'Experience with Backend technologies: NodeJS, Express, NestJS, PHP, Laravel', background_color: '', slide: 1, column: 1, sort: 3 },
    
    // Slide 1, Column 2
    { text: 'Experience with CMS platforms: WordPress, Shopify, Directus, Ghost, Strapi, Contentful, and KeystoneJS', background_color: 'primary_3_25', slide: 1, column: 2, sort: 4 },
    { text: 'Knowledge of modern highly scalable web architectures including cloud services: AWS, Digital Ocean, Tencent, Huawei, Alibaba, Vultr, MongoAtlas and Google Cloud', background_color: '', slide: 1, column: 2, sort: 5 },
    { text: 'Experience in Chrome-Extension and data visualisation libraries: D3.js, Highcharts, Chart.js and Rechart', background_color: 'neutral_2_bg', slide: 1, column: 2, sort: 6 },
    
    // Slide 2, Column 1
    { text: 'Experience in database querying and analytics: MongoDB, PostgreSQL, MySQL, Redis', background_color: 'neutral_2_50', slide: 2, column: 1, sort: 7 },
    { text: 'Experience in code versioning control: Github, Gitlab, Bitbucket', background_color: 'primary_3_15', slide: 2, column: 1, sort: 8 },
    { text: 'Experience using Unix/Linux, Ubuntu, Apache, Nginx, Let\'s Encrypt, PM2', background_color: '', slide: 2, column: 1, sort: 9 },
    
    // Slide 2, Column 2
    { text: 'Experience Docker: Container, Volumes, Images', background_color: 'primary_2', slide: 2, column: 2, sort: 10 },
    { text: 'Experience in developing custom loader or plugin for bundler like vite, rollups, webpack', background_color: '', slide: 2, column: 2, sort: 11 },
    { text: 'Familiar with the Agile software development process, including Sprints and Scrum', background_color: '', slide: 2, column: 2, sort: 12 },
    
    // Slide 2, Column 3
    { text: 'Experience in using AI tools: copilot github, chatGPT to initialize the codes as well as improve programming algorithms', background_color: 'primary_3_25', slide: 2, column: 3, sort: 13 },
    { text: 'Experience to use Google PageSpeed Insights, Chrome DevTools to check, identify problem and improve website performance', background_color: '', slide: 2, column: 3, sort: 14 },
    { text: 'Experience with CI/CD tools such as Circle CI, Gitlab CI, Github Actions, and Vercel', background_color: 'neutral_2_bg', slide: 2, column: 3, sort: 15 }
  ],

  experience: [
    {
      period: "2022 - Present",
      title: "Senior Fullstack Developer",
      company: "TechVSI, Singapore",
      role: "Lead Developer, Full Stack Developer",
      technologies_backend: ["NodeJS", "Express", "NestJS", "PHP", "Laravel"],
      technologies_frontend: ["HTML5", "CSS3", "SCSS", "Bootstrap", "JQuery", "VanillaJS", "ReactJS", "NextJS", "VueJS", "NuxtJS", "TailwindCSS"],
      technologies_platform: ["AWS", "Google Cloud", "Vercel", "Netlify"],
      technologies_database: ["PostgreSQL", "MySQL", "MongoDB", "Redis"],
      details: [
        "Lead a development team to handle a variety of projects",
        "Manage timeline and client expectation",
        "Planning and solution architecture",
        "Apply new processes to the daily improvement of the development team",
        "Using new technologies to solve business requirements"
      ],
      achievements: [
        "Worked with team members and clients to developing Singapore's first Employment Marketplace named WerkSG with 52K+ users",
        "Worked with team members and client to developing Parkstan Livestream Application and admin management portal named RoccoVideo with 93K+ users"
      ],
      sort: 1
    },
    {
      period: "Nov 2020 - 2022",
      title: "WordPress Developer",
      company: "TechVSI, Vietnam",
      role: "Lead Developer, Full Stack Developer",
      technologies_backend: ["PHP", "NodeJS"],
      technologies_frontend: ["HTML5", "CSS3", "SCSS", "Bootstrap", "JQuery", "VanillaJS", "ReactJS", "Liquid", "SvelteJS", "SapperJS", "SvelteKit", "TailwindCSS"],
      technologies_platform: ["WordPress", "Shopify"],
      technologies_database: ["MySQL"],
      details: [
        "Lead a development team to handle a variety of projects",
        "Manage timeline and client expectation",
        "Planning and solution architecture",
        "Apply new processes to the daily improvement of the development team",
        "Using new technologies to solve business requirements"
      ],
      achievements: [],
      sort: 2
    },
    {
      period: "2019 - Nov 2020",
      title: "Frontend Developer",
      company: "Freelance",
      role: "Frontend Developer",
      technologies_backend: [],
      technologies_frontend: ["HTML5", "CSS3", "SCSS", "Bootstrap", "JQuery", "VanillaJS"],
      technologies_platform: ["WordPress"],
      technologies_database: ["MySQL"],
      details: [
        "Developed responsive websites for various clients",
        "Converted PSD designs to pixel-perfect HTML/CSS",
        "Implemented interactive features using JavaScript",
        "Optimized websites for performance and SEO"
      ],
      achievements: [
        "Successfully delivered 20+ projects on time and within budget",
        "Achieved 95%+ client satisfaction rate"
      ],
      sort: 3
    }
  ],

  projects: [
    {
      name: 'WerkSG',
      category: 'Website, App, Admin Portal',
      url: 'https://werk.sg/',
      description: 'Singapore\'s first Employment Marketplace connecting job seekers with employers',
      technologies: ["React", "Node.js", "PostgreSQL", "AWS"],
      gradient: '',
      glass_effect: false,
      overlay_color: 'rgba(93, 53, 221, 0.8)',
      glass_bg_color: '',
      featured: true,
      sort: 1
    },
    {
      name: 'RoccoVideo',
      category: 'Mobile App, Admin Portal',
      url: 'https://roccovideo.com',
      description: 'Livestream application and admin management portal for content creators',
      technologies: ["React Native", "Node.js", "MongoDB", "AWS"],
      gradient: 'radial-gradient(ellipse at 25% 95%, rgba(2,3,21,0) 40%, rgba(93,53,221,0.7) 70%, #5D35DD 85%, #AF87FF 100%)',
      glass_effect: true,
      overlay_color: '',
      glass_bg_color: '',
      featured: false,
      sort: 2
    },
    {
      name: 'ProjectSG',
      category: 'Website, Admin Portal, Website Builder',
      url: 'https://project.sg',
      description: 'Website builder platform with drag-and-drop functionality',
      technologies: ["Vue.js", "Nuxt.js", "Laravel", "MySQL"],
      gradient: '',
      glass_effect: false,
      overlay_color: 'rgba(93, 53, 221, 0.5)',
      glass_bg_color: '',
      featured: false,
      sort: 3
    },
    {
      name: 'E-Commerce Platform',
      category: 'Website, Admin Portal',
      url: 'https://example-ecommerce.com',
      description: 'Full-featured e-commerce platform with payment integration',
      technologies: ["Next.js", "Stripe", "PostgreSQL", "Vercel"],
      gradient: 'linear-gradient(135deg, rgba(93, 53, 221, 0.8) 0%, rgba(175, 135, 255, 0.6) 100%)',
      glass_effect: false,
      overlay_color: '',
      glass_bg_color: '',
      featured: false,
      sort: 4
    }
  ],

  education: [
    {
      year: '2023',
      title: 'AWS Certified Solutions Architect',
      certificate_link: 'https://aws.amazon.com/certification/',
      sort: 1
    },
    {
      year: '2022',
      title: 'Google Cloud Professional Developer',
      certificate_link: 'https://cloud.google.com/certification',
      sort: 2
    },
    {
      year: '2021',
      title: 'React Developer Certification',
      certificate_link: 'https://reactjs.org/',
      sort: 3
    },
    {
      year: '2018',
      title: 'Bachelor of Computer Science',
      certificate_link: '',
      sort: 4
    }
  ],

  social_links: [
    {
      platform: 'github',
      url: 'https://github.com/huynguyen',
      icon: 'github',
      sort: 1
    },
    {
      platform: 'linkedin',
      url: 'https://linkedin.com/in/huynguyen',
      icon: 'linkedin',
      sort: 2
    },
    {
      platform: 'twitter',
      url: 'https://twitter.com/huynguyen',
      icon: 'twitter',
      sort: 3
    },
    {
      platform: 'medium',
      url: 'https://medium.com/@huynguyen',
      icon: 'medium',
      sort: 4
    },
    {
      platform: 'devto',
      url: 'https://dev.to/huynguyen',
      icon: 'dev',
      sort: 5
    }
  ]
};

// Helper function to populate a collection
async function populateCollection(collectionName, data, isArray = true) {
  console.log(`\n📝 Populating ${collectionName} collection...`);
  
  try {
    if (isArray) {
      await directus.request(createItems(collectionName, data));
      console.log(`✅ Successfully added ${data.length} items to ${collectionName}`);
    } else {
      // For singleton collections like profile
      await directus.request(createItem(collectionName, data));
      console.log(`✅ Successfully added profile data to ${collectionName}`);
    }
  } catch (error) {
    console.error(`❌ Error populating ${collectionName}:`, error.message);
    if (error.errors) {
      error.errors.forEach(err => console.error(`   - ${err.message}`));
    }
  }
}

// Main function to populate data
async function populateDirectusData() {
  console.log('🚀 Starting Directus data population...');
  console.log(`📡 Connecting to: ${directusUrl}`);

  try {
    // Authenticate
    if (directusToken) {
      console.log('🔑 Using static token for authentication...');
      directus.setToken(directusToken);
    } else {
      console.log('🔑 Logging in with admin credentials...');
      await directus.request(authentication('login', {
        email: adminEmail,
        password: adminPassword
      }));
    }
    
    console.log('✅ Successfully authenticated with Directus');

    // Populate collections
    await populateCollection('profile', sampleData.profile, false);
    await populateCollection('skills', sampleData.skills);
    await populateCollection('expertise', sampleData.expertise);
    await populateCollection('experience', sampleData.experience);
    await populateCollection('projects', sampleData.projects);
    await populateCollection('education', sampleData.education);
    await populateCollection('social_links', sampleData.social_links);

    console.log('\n🎉 Data population completed successfully!');
    console.log('\n📋 Summary:');
    console.log('✅ Profile data populated');
    console.log('✅ 49 skills added (images need to be uploaded manually)');
    console.log('✅ 15 expertise items added');
    console.log('✅ 3 experience entries added');
    console.log('✅ 4 projects added');
    console.log('✅ 4 education items added');
    console.log('✅ 5 social links added');
    console.log('\n🔗 Next steps:');
    console.log('1. Check your data in Directus admin panel: http://localhost:8055/admin');
    console.log('2. 🖼️  IMPORTANT: Upload skill images manually (see guide below)');
    console.log('3. Upload project images if needed');
    console.log('4. Update your frontend to fetch data from Directus API');
    console.log('5. Customize the data to match your actual profile and projects');
    console.log('\n📖 Skill Images Guide:');
    console.log('   - Go to Content > Skills in Directus admin');
    console.log('   - For each skill, click edit and upload the corresponding image');
    console.log('   - Skill images are located in: public/images/skills/');
    console.log('   - Match skill names with image files (e.g., "React.js" → "reactjs-logo.svg")');

  } catch (error) {
    console.error('❌ Data population failed:', error.message);
    if (error.errors) {
      error.errors.forEach(err => console.error(`   - ${err.message}`));
    }
    process.exit(1);
  }
}

// Run the population script
populateDirectusData();
