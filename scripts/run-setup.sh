#!/bin/bash

# Colors for terminal output
GREEN="\033[0;32m"
YELLOW="\033[1;33m"
RED="\033[0;31m"
NC="\033[0m" # No Color

echo "${YELLOW}Portfolio 2025 - Directus Setup Helper${NC}"
echo "----------------------------------------"

# Check if Directus is running
echo "${YELLOW}Checking if Directus is running...${NC}"

DIRECTUS_URL=${DIRECTUS_URL:-"http://localhost:8055"}

# Try to connect to Directus
if curl -s --head --request GET "$DIRECTUS_URL/server/ping" | grep "200 OK" > /dev/null; then
  echo "${GREEN}✓ Directus is running at $DIRECTUS_URL${NC}"
else
  echo "${RED}✗ Directus is not running at $DIRECTUS_URL${NC}"
  echo "${YELLOW}Starting Docker containers...${NC}"
  
  # Check if we're in the project root
  if [ -f "docker-compose.yml" ]; then
    npm run docker:up
    
    # Wait for <PERSON>us to start
    echo "${YELLOW}Waiting for Directus to start...${NC}"
    for i in {1..30}; do
      if curl -s --head --request GET "$DIRECTUS_URL/server/ping" | grep "200 OK" > /dev/null; then
        echo "${GREEN}✓ Directus is now running!${NC}"
        break
      fi
      
      if [ $i -eq 30 ]; then
        echo "${RED}✗ Timed out waiting for Directus to start${NC}"
        echo "${YELLOW}Please check Docker logs and try again${NC}"
        exit 1
      fi
      
      echo -n "."
      sleep 2
    done
  else
    echo "${RED}✗ Cannot find docker-compose.yml. Are you in the project root?${NC}"
    echo "${YELLOW}Please run this script from the project root directory${NC}"
    exit 1
  fi
fi

# Check if .env file exists and has DIRECTUS_TOKEN
if [ ! -f ".env" ]; then
  echo "${RED}✗ .env file not found${NC}"
  echo "${YELLOW}Creating .env file from .env.example...${NC}"
  
  if [ -f ".env.example" ]; then
    cp .env.example .env
    echo "${GREEN}✓ Created .env file${NC}"
  else
    echo "${RED}✗ .env.example not found${NC}"
    echo "${YELLOW}Please create a .env file with DIRECTUS_URL and DIRECTUS_TOKEN${NC}"
    exit 1
  fi
fi

# Check if DIRECTUS_TOKEN is set
if grep -q "DIRECTUS_TOKEN=" .env && ! grep -q "DIRECTUS_TOKEN=$" .env; then
  echo "${GREEN}✓ DIRECTUS_TOKEN is set in .env${NC}"
else
  echo "${RED}✗ DIRECTUS_TOKEN is not set in .env${NC}"
  echo "${YELLOW}Please get a static token from Directus admin panel:${NC}"
  echo "1. Log in to Directus admin panel ($DIRECTUS_URL/admin)"
  echo "2. Go to User Settings > Token"
  echo "3. Create a new token with admin privileges"
  echo "4. Add it to your .env file as DIRECTUS_TOKEN=your_token_here"
  
  read -p "Would you like to continue anyway? (y/n) " -n 1 -r
  echo
  if [[ ! $REPLY =~ ^[Yy]$ ]]; then
    exit 1
  fi
fi

# Run the setup script
echo "${YELLOW}Running Directus setup script...${NC}"
node scripts/setup-directus.js

# Check if the script was successful
if [ $? -eq 0 ]; then
  echo "${GREEN}✓ Directus setup completed successfully!${NC}"
  echo "${YELLOW}You can now access Directus at $DIRECTUS_URL/admin${NC}"
else
  echo "${RED}✗ Directus setup failed${NC}"
  echo "${YELLOW}Please check the error messages above${NC}"
fi