# Directus Data Population Guide

This guide will help you populate your Directus collections with sample data for your Portfolio 2025 project.

## Prerequisites

1. **Directus is running**: Make sure your Docker containers are up
   ```bash
   npm run docker:up
   ```

2. **Collections are created**: Run the setup script first
   ```bash
   npm run setup:directus
   ```

3. **Authentication configured**: You need either:
   - A static token in your `.env` file: `DIRECTUS_TOKEN=your_token_here`
   - Admin credentials in your `.env` file: `ADMIN_EMAIL` and `ADMIN_PASSWORD`

## Quick Start

Run the data population script:

```bash
npm run populate:directus
```

This will populate all collections with sample data based on your existing codebase.

## What Data Gets Populated

### 1. Profile (Singleton)
- **Name**: <PERSON><PERSON>
- **Title**: Fullstack Developer
- **Bio**: Professional description
- **Contact**: Email, phone, location, website

### 2. Skills (49 items)
Organized by categories:
- **Frontend Technologies** (14 items): HTML5, CSS3, JavaScript, TypeScript, React.js, Vue.js, Next.js, Nuxt.js, etc.
- **Backend Technologies** (8 items): Node.js, Express.js, NestJS, PHP, Laravel, Python, Django, FastAPI
- **Databases** (5 items): PostgreSQL, MySQL, MongoDB, Redis, SQLite
- **Cloud & DevOps** (8 items): AWS, Google Cloud, Docker, Kubernetes, GitHub Actions, etc.
- **Development Tools** (8 items): Git, VS Code, Webpack, Vite, ESLint, Prettier, Jest, Cypress
- **CMS Platforms** (6 items): WordPress, Shopify, Strapi, Contentful, Ghost, Directus

### 3. Expertise (15 items)
Two slides with multiple columns covering:
- **Slide 1**: AWS services, cloud platforms, backend technologies, CMS platforms, web architectures, data visualization
- **Slide 2**: Database technologies, version control, Unix/Linux, Docker, bundlers, Agile, AI tools, performance optimization, CI/CD

### 4. Experience (3 items)
- **Senior Fullstack Developer** at TechVSI, Singapore (2022 - Present)
- **WordPress Developer** at TechVSI, Vietnam (Nov 2020 - 2022)
- **Frontend Developer** Freelance (2019 - Nov 2020)

Each includes technologies, details, and achievements.

### 5. Projects (4 items)
- **WerkSG**: Employment Marketplace (Featured project)
- **RoccoVideo**: Livestream application
- **ProjectSG**: Website builder platform
- **E-Commerce Platform**: Full-featured e-commerce solution

Each includes styling options (gradients, glass effects, overlay colors).

### 6. Education (4 items)
- AWS Certified Solutions Architect (2023)
- Google Cloud Professional Developer (2022)
- React Developer Certification (2021)
- Bachelor of Computer Science (2018)

### 7. Social Links (5 items)
- GitHub
- LinkedIn
- Twitter
- Medium
- Dev.to

## Customizing the Data

After running the script, you should customize the data to match your actual profile:

1. **Update Profile Information**:
   - Change name, title, bio, contact details
   - Upload your actual avatar image
   - Upload your resume file

2. **Modify Skills**:
   - Add/remove skills based on your expertise
   - Upload skill icons/logos to match your design

3. **Update Experience**:
   - Replace with your actual work experience
   - Update company names, periods, achievements
   - Adjust technology lists

4. **Customize Projects**:
   - Replace with your actual projects
   - Upload project images
   - Update URLs and descriptions
   - Adjust styling (gradients, overlays)

5. **Update Education**:
   - Add your actual education and certifications
   - Upload certificate files or add links

6. **Update Social Links**:
   - Replace with your actual social media profiles
   - Add/remove platforms as needed

## Troubleshooting

### Authentication Issues
- **Token not working**: Make sure your token has admin privileges
- **Login failed**: Check your admin email/password in `.env`

### Collection Errors
- **Collection not found**: Run `npm run setup:directus` first
- **Field errors**: Check that all collections were created properly

### Data Conflicts
- **Duplicate entries**: The script will fail if data already exists
- **Clear existing data**: Use Directus admin panel to delete existing items before re-running

### Common Fixes
1. **Clear existing data**: Go to Directus admin → Collections → Delete all items
2. **Re-run setup**: `npm run setup:directus` then `npm run populate:directus`
3. **Check logs**: The script provides detailed error messages

## Next Steps

1. **Access Directus Admin**: http://localhost:8055/admin
2. **Review and customize** all the populated data
3. **Upload images** for skills, projects, and profile
4. **Test your frontend** to ensure it's fetching data correctly
5. **Update your frontend components** to use Directus data instead of hardcoded data

## File Structure

```
scripts/
├── setup-directus.js           # Creates collections and fields
├── populate-directus-data.js   # Populates sample data
└── DATA_POPULATION_GUIDE.md    # This guide
```

## Support

If you encounter issues:
1. Check the console output for detailed error messages
2. Verify your Directus instance is running
3. Ensure your authentication credentials are correct
4. Check that collections exist before populating data
