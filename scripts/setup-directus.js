#!/usr/bin/env node

/**
 * Directus Collection Setup Script
 * 
 * This script automates the creation of collections and fields for the Portfolio 2025 project.
 * It uses the Directus API to programmatically set up the data model.
 * 
 * Usage:
 * 1. Make sure Directus is running (docker-compose up -d)
 * 2. Set DIRECTUS_URL and DIRECTUS_TOKEN in .env file
 * 3. Run: node scripts/setup-directus.js
 */

import { createDirectus, rest, authentication, readItems, createCollection, createField } from '@directus/sdk';
import dotenv from 'dotenv';
import { fileURLToPath } from 'url';
import { dirname, resolve } from 'path';
import fs from 'fs';

// Load environment variables
dotenv.config();

const __filename = fileURLToPath(import.meta.url);
const __dirname = dirname(__filename);
const rootDir = resolve(__dirname, '..');

// Configuration
const directusUrl = process.env.DIRECTUS_URL || 'http://localhost:8055';
const adminEmail = process.env.ADMIN_EMAIL || '<EMAIL>';
const adminPassword = process.env.ADMIN_PASSWORD || 'admin';

// Initialize Directus client
const directus = createDirectus(directusUrl)
  .with(authentication())
  .with(rest());

// Collection definitions
const collections = [
  {
    collection: 'profile',
    meta: {
      collection: 'profile',
      icon: 'person',
      note: 'Personal profile information',
      display_template: '{{name}}',
      singleton: true,
    },
    schema: {
      name: 'profile',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'bio',
        type: 'text',
        meta: {
          interface: 'input-rich-text-html',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'avatar',
        type: 'uuid',
        meta: {
          interface: 'file-image',
          display: 'image',
          special: ['file'],
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'resume_file',
        type: 'uuid',
        meta: {
          interface: 'file',
          display: 'file',
          special: ['file'],
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'skills',
    meta: {
      collection: 'skills',
      icon: 'code',
      note: 'Technical skills',
      display_template: '{{name}}',
    },
    schema: {
      name: 'skills',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'image',
        type: 'uuid',
        meta: {
          interface: 'file-image',
          display: 'image',
          special: ['file'],
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'category',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'formatted-value',
          options: {
            choices: [
              { text: 'Frontend Technologies', value: 'frontend' },
              { text: 'Backend Technologies', value: 'backend' },
              { text: 'Databases', value: 'database' },
              { text: 'Cloud & DevOps', value: 'devops' },
              { text: 'Development Tools', value: 'tools' },
              { text: 'CMS Platforms', value: 'cms' },
            ],
          },
        },
        schema: {
          is_nullable: false,
          default_value: 'frontend',
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'expertise',
    meta: {
      collection: 'expertise',
      icon: 'star',
      note: 'Areas of expertise',
      display_template: '{{text}}',
    },
    schema: {
      name: 'expertise',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'text',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'bg_color_class',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Tailwind CSS class for background color',
        },
        schema: {
          is_nullable: true,
          default_value: 'bg-primary-1',
        },
      },
      {
        field: 'custom_bg_style',
        type: 'text',
        meta: {
          interface: 'input-code',
          display: 'formatted-value',
          note: 'Custom CSS for background (optional)',
          options: {
            language: 'css',
          },
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'slide',
        type: 'integer',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Slide number (1, 2, 3, etc.)',
        },
        schema: {
          is_nullable: false,
          default_value: 1,
        },
      },
      {
        field: 'column',
        type: 'integer',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Column number (1 or 2)',
        },
        schema: {
          is_nullable: false,
          default_value: 1,
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'experience',
    meta: {
      collection: 'experience',
      icon: 'work',
      note: 'Professional experience',
      display_template: '{{title}} at {{company}}',
    },
    schema: {
      name: 'experience',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'period',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Time period (e.g., "2020-2022")',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'company',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'role',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'technologies_backend',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Backend technologies used',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'technologies_frontend',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Frontend technologies used',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'technologies_platform',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Platform technologies used',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'technologies_database',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Database technologies used',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'details',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Job details',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'achievements',
        type: 'json',
        meta: {
          interface: 'list',
          display: 'formatted-value',
          special: ['json'],
          note: 'Key achievements',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'projects',
    meta: {
      collection: 'projects',
      icon: 'folder',
      note: 'Portfolio projects',
      display_template: '{{name}}',
    },
    schema: {
      name: 'projects',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'name',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'category',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'image',
        type: 'uuid',
        meta: {
          interface: 'file-image',
          display: 'image',
          special: ['file'],
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'url',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Project URL',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'gradient',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          display: 'boolean',
          note: 'Use gradient overlay',
        },
        schema: {
          is_nullable: false,
          default_value: false,
        },
      },
      {
        field: 'glass_effect',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          display: 'boolean',
          note: 'Use glass effect',
        },
        schema: {
          is_nullable: false,
          default_value: false,
        },
      },
      {
        field: 'overlay_color',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Overlay color (CSS value)',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'glass_bg_color',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Glass background color (CSS value)',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'featured',
        type: 'boolean',
        meta: {
          interface: 'boolean',
          display: 'boolean',
          note: 'Featured project (large card)',
        },
        schema: {
          is_nullable: false,
          default_value: false,
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'education',
    meta: {
      collection: 'education',
      icon: 'school',
      note: 'Education and certifications',
      display_template: '{{title}}',
    },
    schema: {
      name: 'education',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'year',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'title',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'certificate',
        type: 'uuid',
        meta: {
          interface: 'file',
          display: 'file',
          special: ['file'],
          note: 'Certificate file',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'certificate_link',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'External certificate link (if not uploading a file)',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
  {
    collection: 'social_links',
    meta: {
      collection: 'social_links',
      icon: 'share',
      note: 'Social media links',
      display_template: '{{platform}}',
    },
    schema: {
      name: 'social_links',
    },
    fields: [
      {
        field: 'id',
        type: 'integer',
        meta: {
          hidden: true,
        },
        schema: {
          is_primary_key: true,
          has_auto_increment: true,
        },
      },
      {
        field: 'platform',
        type: 'string',
        meta: {
          interface: 'select-dropdown',
          display: 'formatted-value',
          options: {
            choices: [
              { text: 'GitHub', value: 'github' },
              { text: 'LinkedIn', value: 'linkedin' },
              { text: 'Twitter', value: 'twitter' },
              { text: 'Instagram', value: 'instagram' },
              { text: 'Facebook', value: 'facebook' },
              { text: 'YouTube', value: 'youtube' },
              { text: 'Medium', value: 'medium' },
              { text: 'Dev.to', value: 'devto' },
              { text: 'CodePen', value: 'codepen' },
              { text: 'Other', value: 'other' },
            ],
          },
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'url',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
        },
        schema: {
          is_nullable: false,
        },
      },
      {
        field: 'icon',
        type: 'string',
        meta: {
          interface: 'input',
          display: 'formatted-value',
          note: 'Icon name (optional)',
        },
        schema: {
          is_nullable: true,
        },
      },
      {
        field: 'sort',
        type: 'integer',
        meta: {
          interface: 'input',
          hidden: true,
        },
        schema: {
          is_nullable: true,
        },
      },
    ],
  },
];

// Main function to create collections
async function setupDirectus() {
  console.log('Starting Directus setup...');

  try {
    // Login with admin credentials
    console.log('Logging in to Directus...');
    await directus.login(adminEmail, adminPassword);
    console.log('Successfully logged in to Directus.');

    // Create collections
    for (const collection of collections) {
      console.log(`Creating collection: ${collection.collection}...`);
      
      try {
        // Create the collection
        await directus.request(
          createCollection({
            collection: collection.collection,
            meta: collection.meta,
            schema: collection.schema,
            fields: collection.fields,
          })
        );

        console.log(`Collection ${collection.collection} created successfully with all fields.`);
      } catch (collectionError) {
        console.error(`Error creating collection ${collection.collection}:`, collectionError);
      }
    }
    
    console.log('Directus setup completed successfully!');
  } catch (error) {
    console.error('Error setting up Directus:', error.message);
    process.exit(1);
  }
}

// Run the setup
setupDirectus();