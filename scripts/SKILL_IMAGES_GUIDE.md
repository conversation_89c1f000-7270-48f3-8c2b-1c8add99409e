# Skill Images Upload Guide

After running the data population script, you'll need to manually upload skill images in Directus. This guide shows you exactly which image to upload for each skill.

## Quick Access

1. **Directus Admin**: http://localhost:8055/admin
2. **Skills Collection**: Content > Skills
3. **Image Location**: `public/images/skills/` in your project

## Skill to Image Mapping

### Frontend Technologies
| Skill Name | Image File | Status |
|------------|------------|---------|
| HTML5 | `html5-logo.svg` | ✅ Available |
| CSS3 | `css3-logo.svg` | ✅ Available |
| JavaScript | `javascript-logo.svg` | ✅ Available |
| TypeScript | `typescript-logo.svg` | ✅ Available |
| React.js | `reactjs-logo.svg` | ✅ Available |
| Vue.js | `vuejs-logo.svg` | ✅ Available |
| Next.js | `nextjs-logo.svg` | ✅ Available |
| Nuxt.js | `nuxtjs-logo.svg` | ✅ Available |
| Remix.js | `remixjs-logo.svg` | ✅ Available |
| Gatsby.js | `gatsbyjs-logo.svg` | ✅ Available |
| Svelte | `svelte-logo.svg` | ✅ Available |
| SCSS/Sass | `sass-logo.svg` | ✅ Available |
| TailwindCSS | `tailwindcss-logo.svg` | ✅ Available |
| Bootstrap | `bootstrap-logo.svg` | ✅ Available |

### Backend Technologies
| Skill Name | Image File | Status |
|------------|------------|---------|
| Node.js | `nodejs.svg` | ✅ Available |
| Express.js | `expressjs-logo.svg` | ✅ Available |
| NestJS | `nestjs-logo.svg` | ✅ Available |
| PHP | `php-logo.svg` | ✅ Available |
| Laravel | `laravel-logo.svg` | ✅ Available |
| Python | `python-logo.svg` | ⚠️ Need to add |
| Django | `django-logo.svg` | ⚠️ Need to add |
| FastAPI | `fastapi-logo.svg` | ⚠️ Need to add |

### Databases
| Skill Name | Image File | Status |
|------------|------------|---------|
| PostgreSQL | `postgresql.svg` | ✅ Available |
| MySQL | `mysql.svg` | ✅ Available |
| MongoDB | `mongodb.svg` | ✅ Available |
| Redis | `redis-logo.svg` | ✅ Available |
| SQLite | `sqlite-logo.svg` | ⚠️ Need to add |

### Cloud & DevOps
| Skill Name | Image File | Status |
|------------|------------|---------|
| AWS | `aws-logo.svg` | ✅ Available |
| Google Cloud | `gcp-logo.svg` | ✅ Available |
| Docker | `docker.svg` | ✅ Available |
| Kubernetes | `kubernetes-logo.svg` | ⚠️ Need to add |
| GitHub Actions | `github-logo.svg` | ✅ Available |
| GitLab CI | `gitlab-logo.svg` | ✅ Available |
| Vercel | `vercel-logo.svg` | ✅ Available |
| Netlify | `netlify-logo.svg` | ⚠️ Need to add |

### Development Tools
| Skill Name | Image File | Status |
|------------|------------|---------|
| Git | `git.svg` | ✅ Available |
| VS Code | `vscode-logo.svg` | ⚠️ Need to add |
| Webpack | `webpack-logo.svg` | ✅ Available |
| Vite | `vite-logo.svg` | ✅ Available |
| ESLint | `eslint-logo.svg` | ⚠️ Need to add |
| Prettier | `prettier-logo.svg` | ⚠️ Need to add |
| Jest | `jest-logo.svg` | ⚠️ Need to add |
| Cypress | `cypress-logo.svg` | ⚠️ Need to add |

### CMS Platforms
| Skill Name | Image File | Status |
|------------|------------|---------|
| WordPress | `wordpress-logo.svg` | ✅ Available |
| Shopify | `shopify-logo.svg` | ✅ Available |
| Strapi | `strapi-logo.svg` | ✅ Available |
| Contentful | `contentful-logo.svg` | ✅ Available |
| Ghost | `ghost-logo.svg` | ✅ Available |
| Directus | `directus-logo.svg` | ✅ Available |

## How to Upload Images

### Method 1: Individual Upload (Recommended)
1. Go to **Directus Admin**: http://localhost:8055/admin
2. Navigate to **Content > Skills**
3. Click on a skill to edit it
4. In the **Image** field, click the upload button
5. Select the corresponding image file from `public/images/skills/`
6. Save the skill
7. Repeat for all skills

### Method 2: Bulk Upload (Advanced)
1. Go to **File Library** in Directus admin
2. Upload all skill images at once
3. Go back to **Content > Skills**
4. Edit each skill and select the appropriate image from the file library

## Missing Images

For skills marked with ⚠️ "Need to add", you'll need to:

1. **Find or create the logo** for that technology
2. **Save it** in `public/images/skills/` with the suggested filename
3. **Upload it** to Directus following the steps above

### Suggested Sources for Missing Logos:
- **Official websites** of the technologies
- **GitHub repositories** (often have logos in their README)
- **Logo repositories** like Simple Icons (https://simpleicons.org/)
- **Create your own** using the technology's brand colors

## Tips

### Image Requirements
- **Format**: SVG preferred (scalable), PNG as fallback
- **Size**: Ideally square (1:1 aspect ratio)
- **Background**: Transparent preferred
- **Quality**: High resolution for PNG files

### Naming Convention
- Use lowercase with hyphens: `technology-name-logo.svg`
- Be consistent with existing files
- Include "logo" in the filename for clarity

### Batch Processing
- You can select multiple skills in the Directus admin
- Use bulk actions if available in your Directus version
- Consider creating a custom script if you have many images

## Verification

After uploading all images:

1. **Check the Skills page** in your frontend
2. **Verify all images load** correctly
3. **Test different categories** to ensure all skills show properly
4. **Check responsive behavior** on different screen sizes

## Troubleshooting

### Image Not Showing
- Check the file was uploaded successfully in Directus File Library
- Verify the skill record has the image field populated
- Clear browser cache and refresh

### Wrong Image
- Edit the skill in Directus admin
- Remove the current image and upload the correct one
- Save the changes

### Performance Issues
- Optimize large PNG files before uploading
- Consider converting PNG to SVG for better performance
- Use image compression tools if needed

## Next Steps

Once all skill images are uploaded:

1. **Test your frontend** to ensure images display correctly
2. **Update your components** to use Directus image URLs
3. **Add any missing skills** you might have forgotten
4. **Customize the styling** to match your design system
