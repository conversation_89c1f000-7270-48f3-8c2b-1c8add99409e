{"name": "portfolio-2025", "version": "1.0.0", "private": true, "type": "module", "scripts": {"build": "nuxt build", "dev": "nuxt dev", "generate": "nuxt generate", "preview": "nuxt preview", "postinstall": "nuxt prepare", "docker:up": "docker-compose up -d", "docker:down": "docker-compose down", "setup:directus": "node scripts/setup-directus.js", "populate:directus": "node scripts/populate-directus-data.js", "setup:complete": "node scripts/setup-and-populate.js"}, "dependencies": {"@directus/sdk": "^14.0.0", "@nuxtjs/tailwindcss": "^6.11.0", "gsap": "^3.13.0", "nuxt": "^3.10.0", "nuxt-directus": "^5.5.0", "swiper": "^11.2.8", "tailwindcss": "^4.1.8"}, "devDependencies": {"@nuxt/devtools": "latest", "@nuxt/types": "^2.18.1", "@tailwindcss/vite": "^4.1.8", "@types/node": "^20.11.0", "dotenv": "^16.4.5", "typescript": "^5.6.2", "vue-tsc": "^2.0.29"}, "packageManager": "yarn@4.9.1+sha512.f95ce356460e05be48d66401c1ae64ef84d163dd689964962c6888a9810865e39097a5e9de748876c2e0bf89b232d583c33982773e9903ae7a76257270986538"}