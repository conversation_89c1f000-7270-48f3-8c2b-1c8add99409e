# Portfolio 2025

A modern portfolio website built with Nuxt 3, TypeScript, Tailwind CSS v4, Directus CMS, and GSAP animations.

## Tech Stack

- **Frontend**: Nuxt.js 3 + TypeScript + Tailwind CSS v4 + GSAP animations
- **CMS**: Directus
- **Database**: PostgreSQL
- **Containerization**: Docker

## Project Structure

The project follows the Atomic Design methodology:

```
components/
├── atoms/      # Button, Input (basic elements)
├── molecules/  # ProjectCard, CategoryButton (groups)
├── organisms/  # Header, ContactForm (complex)
└── templates/  # PageTemplate, FullWidthTemplate
```

## Getting Started

### Prerequisites

- Node.js (v18+)
- Docker and Docker Compose
- npm or yarn

### Installation

1. Clone the repository

```bash
git clone <repository-url>
cd Portfolio-2025
```

2. Install dependencies

```bash
npm install
# or
yarn install
```

3. Create a `.env` file from the example

```bash
cp .env.example .env
```

4. Start the Docker containers (Directus CMS and PostgreSQL)

```bash
npm run docker:up
```

5. Set up Directus collections

```bash
# Option 1: Using the helper script (recommended)
./scripts/run-setup.sh

# Option 2: Manually
npm run setup:directus
```

6. Start the development server

```bash
npm run dev
```

7. Open your browser and navigate to:
   - Frontend: http://localhost:3000
   - Directus CMS: http://localhost:8055/admin

## Directus CMS Setup

The project uses Directus as a headless CMS to manage content. The setup script creates the following collections:

1. **Profile** - Personal information (singleton)
   - Name, title, bio, avatar, resume file

2. **Skills** - Technical skills
   - Name, image, category (frontend, backend, database, devops, tools, cms)

3. **Expertise** - Areas of expertise
   - Text, background color, custom styling, slide/column positioning

4. **Experience** - Professional experience
   - Period, title, company, role, technologies, details, achievements

5. **Projects** - Portfolio projects
   - Name, category, image, URL, styling options

6. **Education** - Education and certifications
   - Year, title, certificate file, certificate link

7. **Social Links** - Social media profiles
   - Platform, URL, icon

For more details on the Directus setup, see the [scripts/README.md](scripts/README.md) file.

## Development

### Key Commands

```bash
npm run dev          # Start development server
npm run build        # Build for production
npm run generate     # Generate static site
npm run preview      # Preview production build
npm run docker:up    # Start Docker containers
npm run docker:down  # Stop Docker containers
npm run setup:directus # Set up Directus collections
```

### Design System

The project uses Tailwind CSS v4 with a custom design system:

- **Colors**: `neutral-1` to `neutral-7` (light→dark), `primary-1`, `primary-2`
- **Responsive**: Mobile-first with `md:`, `lg:` prefixes
- **Typography**: Manrope font with custom heading classes

### Component Patterns

Components follow the Vue 3 Composition API with TypeScript:

```vue
<script setup lang="ts">
interface Props {
  title: string;
  items: Array<{ id: string; name: string }>;
}

const props = defineProps<Props>();
const emit = defineEmits<{ select: [item: any] }>();
</script>
```

## License

MIT